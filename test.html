<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puter.js AI 图像分析</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .image-container img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .result-container {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Puter.js AI 图像分析</h1>

        <div class="image-container">
            <img id="targetImage" src="https://raw.githubusercontent.com/dfdfou/myimage/main/女.png" alt="待分析图像">
        </div>

        <div id="authSection" style="text-align: center; display: none;">
            <div class="result-container" style="background-color: #fff3cd; border-left-color: #ffc107;">
                <h3>🔐 需要登录</h3>
                <p>要使用 AI 图像分析功能，您需要先登录 Puter 账户。</p>
                <button id="signInBtn" onclick="signInUser()">登录 Puter</button>
            </div>
        </div>

        <div id="mainSection" style="text-align: center;">
            <div id="userInfo" style="display: none; margin-bottom: 20px; padding: 10px; background-color: #e8f5e8; border-radius: 5px;">
                <span id="userDisplay"></span>
                <button onclick="signOutUser()" style="margin-left: 10px; background-color: #6c757d;">退出登录</button>
            </div>
            <button id="analyzeBtn" onclick="analyzeImage()">分析图像</button>
            <button id="retryBtn" onclick="analyzeImage()" style="display: none;">重试</button>
        </div>

        <div id="result" class="result-container" style="display: none;">
            <h3>AI 分析结果：</h3>
            <div id="resultContent"></div>
        </div>
    </div>

    <script src="https://js.puter.com/v2/"></script>
    <script>
        let isAnalyzing = false;
        let currentUser = null;

        // 检查用户登录状态
        async function checkAuthStatus() {
            try {
                const isSignedIn = await puter.auth.isSignedIn();
                if (isSignedIn) {
                    currentUser = await puter.auth.getUser();
                    showMainInterface();
                    updateUserDisplay();
                } else {
                    showAuthInterface();
                }
            } catch (error) {
                console.error('检查认证状态失败:', error);
                showAuthInterface();
            }
        }

        // 显示认证界面
        function showAuthInterface() {
            document.getElementById('authSection').style.display = 'block';
            document.getElementById('mainSection').style.display = 'none';
            document.getElementById('result').style.display = 'none';
        }

        // 显示主界面
        function showMainInterface() {
            document.getElementById('authSection').style.display = 'none';
            document.getElementById('mainSection').style.display = 'block';
        }

        // 更新用户显示信息
        function updateUserDisplay() {
            if (currentUser) {
                const userInfo = document.getElementById('userInfo');
                const userDisplay = document.getElementById('userDisplay');
                userDisplay.textContent = `👋 欢迎，${currentUser.username || currentUser.email || '用户'}！`;
                userInfo.style.display = 'block';
            }
        }

        // 用户登录
        async function signInUser() {
            try {
                const signInBtn = document.getElementById('signInBtn');
                signInBtn.disabled = true;
                signInBtn.textContent = '正在登录...';

                await puter.auth.signIn();
                currentUser = await puter.auth.getUser();
                showMainInterface();
                updateUserDisplay();

                // 登录成功后自动分析图像
                setTimeout(analyzeImage, 1000);

            } catch (error) {
                console.error('登录失败:', error);
                showResult(`登录失败：${error.message || error}`, true);
            } finally {
                const signInBtn = document.getElementById('signInBtn');
                signInBtn.disabled = false;
                signInBtn.textContent = '登录 Puter';
            }
        }

        // 用户退出登录
        async function signOutUser() {
            try {
                await puter.auth.signOut();
                currentUser = null;
                showAuthInterface();
            } catch (error) {
                console.error('退出登录失败:', error);
            }
        }

        function showLoading() {
            const resultDiv = document.getElementById('result');
            const contentDiv = document.getElementById('resultContent');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const retryBtn = document.getElementById('retryBtn');

            resultDiv.style.display = 'block';
            resultDiv.className = 'result-container';
            contentDiv.innerHTML = '<div class="loading">🔄 AI 正在分析图像，请稍候...</div>';
            analyzeBtn.disabled = true;
            retryBtn.style.display = 'none';
            isAnalyzing = true;
        }

        function showResult(response, isError = false) {
            const resultDiv = document.getElementById('result');
            const contentDiv = document.getElementById('resultContent');
            const analyzeBtn = document.getElementById('analyzeBtn');
            const retryBtn = document.getElementById('retryBtn');

            resultDiv.style.display = 'block';
            resultDiv.className = isError ? 'result-container error' : 'result-container success';
            contentDiv.innerHTML = isError ?
                `<strong>❌ 错误：</strong>${response}` :
                `<div style="white-space: pre-wrap;">${response}</div>`;

            analyzeBtn.disabled = false;
            if (isError) {
                retryBtn.style.display = 'inline-block';
            }
            isAnalyzing = false;
        }

        async function analyzeImage() {
            if (isAnalyzing) return;

            // 检查用户是否已登录
            try {
                const isSignedIn = await puter.auth.isSignedIn();
                if (!isSignedIn) {
                    showResult('请先登录 Puter 账户才能使用 AI 分析功能。', true);
                    showAuthInterface();
                    return;
                }
            } catch (error) {
                console.error('检查登录状态失败:', error);
                showResult('无法验证登录状态，请刷新页面重试。', true);
                return;
            }

            showLoading();

            try {
                const response = await puter.ai.chat(
                    "请详细描述这张图片中你看到的内容，包括人物、服装、背景、色彩等细节。",
                    "https://raw.githubusercontent.com/dfdfou/myimage/main/女.png"
                );

                showResult(response);

            } catch (error) {
                console.error('Puter.js AI 调用失败:', error);
                let errorMessage = error.message || error;

                // 处理常见的认证错误
                if (errorMessage.includes('not allowed') || errorMessage.includes('unauthorized') || errorMessage.includes('authentication')) {
                    errorMessage = '认证失败，请重新登录。';
                    showAuthInterface();
                } else if (errorMessage.includes('quota') || errorMessage.includes('limit')) {
                    errorMessage = '已达到使用限制，请稍后再试或检查您的 Puter 账户余额。';
                } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
                    errorMessage = '网络连接错误，请检查网络连接后重试。';
                }

                showResult(`调用 Puter.js AI 服务时发生错误：${errorMessage}`, true);
            }
        }

        // 页面加载完成后检查认证状态
        window.addEventListener('load', function() {
            setTimeout(checkAuthStatus, 500);
        });

        // 图像加载错误处理
        document.getElementById('targetImage').addEventListener('error', function() {
            showResult('图像加载失败，请检查图像URL是否有效。', true);
        });
    </script>
</body>
</html>